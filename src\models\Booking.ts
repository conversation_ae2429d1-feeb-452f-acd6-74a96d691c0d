import { DataTypes, Optional, Op } from "sequelize";
import sequelize from "../config/database";
import User from "./User";
import Service from "./Service";
import BaseModel from "./BaseModel";
import Team from './Team';

interface BookingAttributes {
    id: number;
    userId: number;
    serviceId: number;
    teamId?: number;
    bookingDate: Date;
    startTime: Date;
    endTime: Date;
    status: "pending" | "confirmed" | "in_progress" | "completed" | "cancelled";
    address: string;
    notes?: string;
    createdAt?: Date;
    updatedAt?: Date;
}

interface BookingCreationAttributes
    extends Optional<
        BookingAttributes,
        "id" | "teamId" | "createdAt" | "updatedAt" | "notes"
    > {}

class Booking
    extends BaseModel<BookingAttributes, BookingCreationAttributes>
    implements BookingAttributes
{
    public id!: number;
    public userId!: number;
    public serviceId!: number;
    public teamId?: number;
    public bookingDate!: Date;
    public startTime!: Date;
    public endTime!: Date;
    public status!: "pending" | "confirmed" | "in_progress" | "completed" | "cancelled";
    public address!: string;
    public notes?: string;
    public readonly createdAt!: Date;
    public readonly updatedAt!: Date;

    // Association properties
    public User?: User;
    public Service?: Service;
    public Team?: any;

    // Static method to check team availability
    static async isTeamAvailable(
        teamId: number,
        startTime: Date,
        endTime: Date,
        excludeBookingId?: number
    ): Promise<boolean> {

        const whereClause: any = {
            teamId,
            status: {
                [Op.in]: ['confirmed', 'in_progress']
            },
            [Op.or]: [
                // New booking starts during existing booking
                {
                    startTime: { [Op.lte]: startTime },
                    endTime: { [Op.gt]: startTime }
                },
                // New booking ends during existing booking
                {
                    startTime: { [Op.lt]: endTime },
                    endTime: { [Op.gte]: endTime }
                },
                // New booking completely contains existing booking
                {
                    startTime: { [Op.gte]: startTime },
                    endTime: { [Op.lte]: endTime }
                }
            ]
        };

        // Exclude current booking if updating
        if (excludeBookingId) {
            whereClause.id = { [Op.ne]: excludeBookingId };
        }

        const conflictingBookings = await this.countRecords({ where: whereClause });
        return conflictingBookings === 0;
    }

    // Static method to find available teams for a time slot
    static async findAvailableTeams(startTime: Date, endTime: Date): Promise<number[]> {

        // Get all active teams
        const activeTeams = await Team.getActiveTeams();
        const availableTeamIds: number[] = [];

        for (const team of activeTeams) {
            const isAvailable = await this.isTeamAvailable(team.id, startTime, endTime);
            if (isAvailable) {
                availableTeamIds.push(team.id);
            }
        }

        return availableTeamIds;
    }

    // Static method to get team schedule for a date
    static async getTeamSchedule(teamId: number, date: Date): Promise<Booking[]> {

        const startOfDay = new Date(date);
        startOfDay.setHours(0, 0, 0, 0);

        const endOfDay = new Date(date);
        endOfDay.setHours(23, 59, 59, 999);

        return this.findAll({
            where: {
                teamId,
                status: {
                    [Op.in]: ['confirmed', 'in_progress', 'completed']
                },
                startTime: {
                    [Op.gte]: startOfDay,
                    [Op.lte]: endOfDay
                }
            },
            order: [['startTime', 'ASC']],
            include: [
                { model: User, attributes: ['firstName', 'lastName', 'email', 'phone'] },
                { model: Service, attributes: ['name', 'duration'] }
            ]
        });
    }

    // Static method to calculate end time based on service duration
    static calculateEndTime(startTime: Date, serviceDuration: number): Date {
        const endTime = new Date(startTime);
        endTime.setMinutes(endTime.getMinutes() + serviceDuration);
        return endTime;
    }

    // Instance method to check if booking can be rescheduled
    public async canReschedule(newStartTime: Date, newEndTime: Date): Promise<boolean> {
        if (!this.teamId) {
            return true; // No team assigned yet
        }

        return Booking.isTeamAvailable(this.teamId, newStartTime, newEndTime, this.id);
    }

    // Instance method to get booking duration in minutes
    public getDurationMinutes(): number {
        return Math.round((this.endTime.getTime() - this.startTime.getTime()) / (1000 * 60));
    }

    // Instance method to check if booking is in progress
    public isInProgress(): boolean {
        const now = new Date();
        return this.status === 'confirmed' &&
               this.startTime <= now &&
               this.endTime > now;
    }

    // Instance method to check if booking is overdue
    public isOverdue(): boolean {
        const now = new Date();
        return this.status === 'confirmed' && this.endTime < now;
    }
}

Booking.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        userId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: "users",
                key: "id",
            },
        },
        serviceId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: "services",
                key: "id",
            },
        },
        teamId: {
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: "teams",
                key: "id",
            },
            onDelete: "SET NULL",
            onUpdate: "CASCADE",
        },
        bookingDate: {
            type: DataTypes.DATEONLY,
            allowNull: false,
        },
        startTime: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        endTime: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        status: {
            type: DataTypes.ENUM(
                "pending",
                "confirmed",
                "in_progress",
                "completed",
                "cancelled"
            ),
            defaultValue: "pending",
        },
        address: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        notes: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
    },
    {
        sequelize,
        modelName: "Booking",
        tableName: "bookings",
    }
);

// Define associations
Booking.belongsTo(User, { foreignKey: "userId" });
Booking.belongsTo(Service, { foreignKey: "serviceId" });

export default Booking;
