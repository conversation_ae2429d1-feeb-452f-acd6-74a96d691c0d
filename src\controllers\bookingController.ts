import { Request, Response } from "express";
import { Booking, Service, User, Team } from "../models";

export const getAllBookings = async (req: Request, res: Response) => {
    try {
        // Admin and managers can see all bookings, users can only see their own
        const canViewAll =
            req.user?.roles.includes("admin") ||
            req.user?.roles.includes("manager");
        const where = canViewAll ? {} : { userId: req.user?.id };

        const bookings = await Booking.findAllRecords({
            where,
            include: [
                {
                    model: User,
                    attributes: [
                        "id",
                        "firstName",
                        "lastName",
                        "email",
                        "phone",
                    ],
                },
                { model: Service },
                {
                    model: Team,
                    attributes: ["id", "name", "description"],
                },
            ],
            order: [["startTime", "ASC"]],
        });

        res.status(200).json(bookings);
    } catch (error) {
        console.error("Error fetching bookings:", error);
        res.status(500).json({ message: "Error fetching bookings" });
    }
};

export const getBookingById = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;

        const booking = await Booking.findById(id, {
            include: [
                {
                    model: User,
                    attributes: [
                        "id",
                        "firstName",
                        "lastName",
                        "email",
                        "phone",
                    ],
                },
                { model: Service },
            ],
        });

        if (!booking) {
            return res.status(404).json({ message: "Booking not found" });
        }

        // Check if the user is authorized to view this booking
        const canView =
            req.user?.roles.includes("admin") ||
            req.user?.roles.includes("manager") ||
            booking.userId === req.user?.id;

        if (!canView) {
            return res
                .status(403)
                .json({ message: "Not authorized to view this booking" });
        }

        res.status(200).json(booking);
    } catch (error) {
        console.error("Error fetching booking:", error);
        res.status(500).json({ message: "Error fetching booking" });
    }
};

export const createBooking = async (req: Request, res: Response) => {
    try {
        const { serviceId, bookingDate, startTime, address, notes } = req.body;
        const userId = req.user?.id;

        // Validate required fields
        if (!serviceId || !bookingDate || !startTime || !address) {
            return res.status(400).json({
                message:
                    "Service ID, booking date, start time, and address are required",
            });
        }

        if (!userId) {
            return res.status(401).json({ message: "User not authenticated" });
        }

        // Check if service exists
        const service = await Service.findById(serviceId);
        if (!service) {
            return res.status(404).json({ message: "Service not found" });
        }

        // Parse and validate times
        const startDateTime = new Date(startTime);
        const endDateTime = Booking.calculateEndTime(
            startDateTime,
            service.duration
        );

        // Validate booking time is in the future
        const now = new Date();
        if (startDateTime <= now) {
            return res.status(400).json({
                message: "Booking time must be in the future",
            });
        }

        // Validate business hours (8 AM to 10 PM)
        const startHour = startDateTime.getHours();
        const endHour = endDateTime.getHours();
        if (startHour < 8 || endHour > 22) {
            return res.status(400).json({
                message: "Bookings are only available between 8 AM and 10 PM",
            });
        }

        // Find available teams for the requested time slot
        const availableTeamIds = await Booking.findAvailableTeams(
            startDateTime,
            endDateTime
        );

        if (availableTeamIds.length === 0) {
            return res.status(409).json({
                message:
                    "No teams available for the requested time slot. Please choose a different time.",
                suggestedTimes: await getSuggestedTimes(
                    startDateTime,
                    service.duration
                ),
            });
        }

        // Assign the first available team
        const assignedTeamId = availableTeamIds[0];

        // Create the booking
        const booking = await Booking.createRecord({
            userId,
            serviceId,
            teamId: assignedTeamId,
            bookingDate: new Date(bookingDate),
            startTime: startDateTime,
            endTime: endDateTime,
            // status: "pending",
            status: "confirmed",
            address,
            notes,
        });

        // Fetch the created booking with associations
        const createdBooking = await Booking.findById(booking.id, {
            include: [
                {
                    model: User,
                    attributes: [
                        "id",
                        "firstName",
                        "lastName",
                        "email",
                        "phone",
                    ],
                },
                { model: Service },
                {
                    model: Team,
                    attributes: ["id", "name", "description"],
                },
            ],
        });

        res.status(201).json({
            message: "Booking created successfully",
            booking: createdBooking,
            assignedTeam: {
                id: assignedTeamId,
                message: `Assigned to team ID ${assignedTeamId}`,
            },
        });
    } catch (error) {
        console.error("Error creating booking:", error);
        res.status(500).json({ message: "Error creating booking" });
    }
};

// Helper function to suggest alternative times
async function getSuggestedTimes(
    requestedTime: Date,
    serviceDuration: number
): Promise<string[]> {
    const suggestions: string[] = [];
    const baseDate = new Date(requestedTime);

    // Try next 3 hours on the same day
    for (let i = 1; i <= 3; i++) {
        const suggestedStart = new Date(baseDate);
        suggestedStart.setHours(baseDate.getHours() + i);

        if (suggestedStart.getHours() <= 17) {
            // Before 6 PM
            const suggestedEnd = Booking.calculateEndTime(
                suggestedStart,
                serviceDuration
            );
            const availableTeams = await Booking.findAvailableTeams(
                suggestedStart,
                suggestedEnd
            );

            if (availableTeams.length > 0) {
                suggestions.push(suggestedStart.toISOString());
            }
        }
    }

    // Try next day same time if no suggestions found
    if (suggestions.length === 0) {
        const nextDay = new Date(baseDate);
        nextDay.setDate(nextDay.getDate() + 1);

        const nextDayEnd = Booking.calculateEndTime(nextDay, serviceDuration);
        const availableTeams = await Booking.findAvailableTeams(
            nextDay,
            nextDayEnd
        );

        if (availableTeams.length > 0) {
            suggestions.push(nextDay.toISOString());
        }
    }

    return suggestions;
}

export const updateBookingStatus = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const { status } = req.body;

        const booking = await Booking.findById(id);

        if (!booking) {
            return res.status(404).json({ message: "Booking not found" });
        }

        // Only admin and managers can update booking status
        const canUpdateStatus =
            req.user?.roles.includes("admin") ||
            req.user?.roles.includes("manager");
        if (!canUpdateStatus) {
            return res
                .status(403)
                .json({ message: "Not authorized to update booking status" });
        }

        await booking.update({ status });

        res.status(200).json({
            message: "Booking status updated successfully",
            booking,
        });
    } catch (error) {
        console.error("Error updating booking status:", error);
        res.status(500).json({ message: "Error updating booking status" });
    }
};

export const cancelBooking = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;

        const booking = await Booking.findById(id);

        if (!booking) {
            return res.status(404).json({ message: "Booking not found" });
        }

        // Check if the user is authorized to cancel this booking
        const canCancel =
            req.user?.roles.includes("admin") ||
            req.user?.roles.includes("manager") ||
            booking.userId === req.user?.id;

        if (!canCancel) {
            return res
                .status(403)
                .json({ message: "Not authorized to cancel this booking" });
        }

        // Only allow cancellation if booking is pending or confirmed
        if (booking.status !== "pending" && booking.status !== "confirmed") {
            return res.status(400).json({
                message: `Cannot cancel booking with status: ${booking.status}`,
            });
        }

        await booking.update({ status: "cancelled" });

        res.status(200).json({
            message: "Booking cancelled successfully",
            booking,
        });
    } catch (error) {
        console.error("Error cancelling booking:", error);
        res.status(500).json({ message: "Error cancelling booking" });
    }
};

// Get team availability for a specific date
export const getTeamAvailability = async (req: Request, res: Response) => {
    try {
        const { date } = req.params;

        if (!date) {
            return res.status(400).json({ message: "Date is required" });
        }

        const requestedDate = new Date(date);
        const teams = await Team.getActiveTeams();

        const teamAvailability = await Promise.all(
            teams.map(async (team) => {
                const schedule = await Booking.getTeamSchedule(
                    team.id,
                    requestedDate
                );
                return {
                    teamId: team.id,
                    teamName: team.name,
                    bookings: schedule.map((booking) => ({
                        id: booking.id,
                        startTime: booking.startTime,
                        endTime: booking.endTime,
                        status: booking.status,
                        service: booking.Service?.name,
                        customer: `${booking.User?.firstName} ${booking.User?.lastName}`,
                    })),
                };
            })
        );

        res.status(200).json({
            message: "Team availability retrieved successfully",
            date: requestedDate.toISOString().split("T")[0],
            teams: teamAvailability,
        });
    } catch (error) {
        console.error("Error getting team availability:", error);
        res.status(500).json({ message: "Error getting team availability" });
    }
};

// Check availability for a specific time slot
export const checkTimeSlotAvailability = async (
    req: Request,
    res: Response
) => {
    try {
        const { startTime, duration } = req.body;

        if (!startTime || !duration) {
            return res.status(400).json({
                message: "Start time and duration are required",
            });
        }

        const startDateTime = new Date(startTime);
        const endDateTime = Booking.calculateEndTime(
            startDateTime,
            parseInt(duration)
        );

        const availableTeamIds = await Booking.findAvailableTeams(
            startDateTime,
            endDateTime
        );
        const availableTeams = await Promise.all(
            availableTeamIds.map(async (teamId) => {
                const team = await Team.findById(teamId);
                return {
                    id: team?.id,
                    name: team?.name,
                    description: team?.description,
                };
            })
        );

        res.status(200).json({
            message: "Availability check completed",
            timeSlot: {
                startTime: startDateTime.toISOString(),
                endTime: endDateTime.toISOString(),
                duration: duration,
            },
            available: availableTeams.length > 0,
            availableTeams,
            totalAvailableTeams: availableTeams.length,
        });
    } catch (error) {
        console.error("Error checking availability:", error);
        res.status(500).json({ message: "Error checking availability" });
    }
};

// Reschedule a booking
export const rescheduleBooking = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const { newStartTime } = req.body;

        if (!newStartTime) {
            return res
                .status(400)
                .json({ message: "New start time is required" });
        }

        const booking = await Booking.findById(parseInt(id), {
            include: [Service],
        });

        if (!booking) {
            return res.status(404).json({ message: "Booking not found" });
        }

        // Check authorization
        const canReschedule =
            req.user?.roles.includes("admin") ||
            req.user?.roles.includes("manager") ||
            booking.userId === req.user?.id;

        if (!canReschedule) {
            return res.status(403).json({
                message: "Not authorized to reschedule this booking",
            });
        }

        // Only allow rescheduling pending or confirmed bookings
        if (!['pending', 'confirmed'].includes(booking.status)) {
            return res.status(400).json({
                message: `Cannot reschedule booking with status: ${booking.status}`,
            });
        }

        const newStartDateTime = new Date(newStartTime);
        const newEndDateTime = Booking.calculateEndTime(
            newStartDateTime,
            booking.Service!.duration
        );

        // Check if the booking can be rescheduled to the new time
        const canRescheduleToNewTime = await booking.canReschedule(
            newStartDateTime,
            newEndDateTime
        );

        if (!canRescheduleToNewTime) {
            // Find alternative times
            const suggestedTimes = await getSuggestedTimes(
                newStartDateTime,
                booking.Service!.duration
            );
            return res.status(409).json({
                message: "The requested time slot is not available",
                suggestedTimes,
            });
        }

        // Update the booking
        await booking.update({
            startTime: newStartDateTime,
            endTime: newEndDateTime,
            bookingDate: new Date(newStartDateTime.toISOString().split("T")[0]),
        });

        // Fetch updated booking with associations
        const updatedBooking = await Booking.findById(booking.id, {
            include: [
                { model: User, attributes: ["firstName", "lastName", "email"] },
                { model: Service },
                { model: Team, attributes: ["id", "name"] },
            ],
        });

        res.status(200).json({
            message: "Booking rescheduled successfully",
            booking: updatedBooking,
        });
    } catch (error) {
        console.error("Error rescheduling booking:", error);
        res.status(500).json({ message: "Error rescheduling booking" });
    }
};

// Get booking statistics
export const getBookingStatistics = async (req: Request, res: Response) => {
    try {
        const { startDate, endDate } = req.query;
        const { Op } = require("sequelize");

        let dateFilter = {};
        if (startDate && endDate) {
            dateFilter = {
                bookingDate: {
                    [Op.between]: [
                        new Date(startDate as string),
                        new Date(endDate as string),
                    ],
                },
            };
        }

        const [
            totalBookings,
            pendingBookings,
            confirmedBookings,
            completedBookings,
            cancelledBookings,
        ] = await Promise.all([
            Booking.countRecords({ where: dateFilter }),
            Booking.countRecords({
                where: { ...dateFilter, status: "pending" },
            }),
            Booking.countRecords({
                where: { ...dateFilter, status: "confirmed" },
            }),
            Booking.countRecords({
                where: { ...dateFilter, status: "completed" },
            }),
            Booking.countRecords({
                where: { ...dateFilter, status: "cancelled" },
            }),
        ]);

        // Team utilization
        const teams = await Team.getActiveTeams();
        const teamUtilization = await Promise.all(
            teams.map(async (team) => {
                const teamBookings = await Booking.countRecords({
                    where: {
                        ...dateFilter,
                        teamId: team.id,
                        status: { [Op.in]: ["confirmed", "completed"] },
                    },
                });
                return {
                    teamId: team.id,
                    teamName: team.name,
                    bookingsCount: teamBookings,
                };
            })
        );

        res.status(200).json({
            message: "Booking statistics retrieved successfully",
            period: {
                startDate: startDate || "All time",
                endDate: endDate || "All time",
            },
            statistics: {
                total: totalBookings,
                pending: pendingBookings,
                confirmed: confirmedBookings,
                completed: completedBookings,
                cancelled: cancelledBookings,
                completionRate:
                    totalBookings > 0
                        ? ((completedBookings / totalBookings) * 100).toFixed(
                              2
                          ) + "%"
                        : "0%",
            },
            teamUtilization,
        });
    } catch (error) {
        console.error("Error getting booking statistics:", error);
        res.status(500).json({ message: "Error getting booking statistics" });
    }
};
