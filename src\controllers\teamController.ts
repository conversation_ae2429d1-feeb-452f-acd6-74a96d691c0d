import { Request, Response } from "express";
import { Team, User, TeamUser } from "../models";

// Get all teams
export const getAllTeams = async (req: Request, res: Response) => {
    try {
        const { includeInactive } = req.query;
        const whereClause =
            includeInactive === "true" ? {} : { isActive: true };

        const teams = await Team.findAll({
            where: whereClause,
            include: [
                {
                    model: User,
                    as: "Leader",
                    attributes: ["id", "firstName", "lastName", "email"],
                },
            ],
            order: [["name", "ASC"]],
        });

        // Get member count for each team
        const teamsWithStats = await Promise.all(
            teams.map(async (team) => {
                const memberCount = await team.getMembersCount();
                return {
                    ...team.toJSON(),
                    memberCount,
                };
            })
        );

        res.status(200).json({
            message: "Teams retrieved successfully",
            teams: teamsWithStats,
        });
    } catch (error) {
        console.error("Get teams error:", error);
        res.status(500).json({ message: "Error retrieving teams" });
    }
};

// Get team by ID
export const getTeamById = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;

        const team = await Team.findById(parseInt(id), {
            include: [
                {
                    model: User,
                    as: "Leader",
                    attributes: ["id", "firstName", "lastName", "email"],
                },
            ],
        });

        if (!team) {
            return res.status(404).json({ message: "Team not found" });
        }

        // Get team members and statistics
        const [members, stats] = await Promise.all([
            team.getMembers(),
            TeamUser.getTeamStats(team.id)
        ]);

        res.status(200).json({
            message: "Team retrieved successfully",
            team: {
                ...team.toJSON(),
                members,
                stats,
            },
        });
    } catch (error) {
        console.error("Get team error:", error);
        res.status(500).json({ message: "Error retrieving team" });
    }
};

// Create new team
export const createTeam = async (req: Request, res: Response) => {
    try {
        const { name, description, leaderId } = req.body;

        if (!name) {
            return res.status(400).json({ message: "Team name is required" });
        }

        // Check if team name already exists
        const existingTeam = await Team.findByName(name);
        if (existingTeam) {
            return res
                .status(400)
                .json({ message: "Team name already exists" });
        }

        // If leaderId is provided, check if user exists
        if (leaderId) {
            const leader = await User.findById(leaderId);
            if (!leader) {
                return res
                    .status(404)
                    .json({ message: "Leader user not found" });
            }
        }

        const team = await Team.createRecord({
            name,
            description,
            leaderId,
            isActive: true,
        });

        // If leader is specified, add them to the team
        if (leaderId) {
            await TeamUser.addUserToTeam(
                leaderId,
                team.id,
                "lead",
                req.user?.id
            );
        }

        res.status(201).json({
            message: "Team created successfully",
            team: team.toJSON(),
        });
    } catch (error) {
        console.error("Create team error:", error);
        res.status(500).json({ message: "Error creating team" });
    }
};

// Update team
export const updateTeam = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const { name, description, leaderId, isActive } = req.body;

        const team = await Team.findById(parseInt(id));
        if (!team) {
            return res.status(404).json({ message: "Team not found" });
        }

        // Check if new name conflicts with existing team
        if (name && name !== team.name) {
            const existingTeam = await Team.findByName(name);
            if (existingTeam) {
                return res
                    .status(400)
                    .json({ message: "Team name already exists" });
            }
        }

        // If leaderId is being changed, validate new leader
        if (leaderId && leaderId !== team.leaderId) {
            const newLeader = await User.findById(leaderId);
            if (!newLeader) {
                return res
                    .status(404)
                    .json({ message: "New leader user not found" });
            }

            // Add new leader to team if not already a member
            const isLeaderInTeam = await newLeader.isInTeam(team.id);
            if (!isLeaderInTeam) {
                await TeamUser.addUserToTeam(
                    leaderId,
                    team.id,
                    "lead",
                    req.user?.id
                );
            } else {
                // Update existing member's role to lead
                await TeamUser.updateUserRole(leaderId, team.id, "lead");
            }
        }

        await team.update({
            name: name || team.name,
            description:
                description !== undefined ? description : team.description,
            leaderId: leaderId !== undefined ? leaderId : team.leaderId,
            isActive: isActive !== undefined ? isActive : team.isActive,
        });

        res.status(200).json({
            message: "Team updated successfully",
            team: team.toJSON(),
        });
    } catch (error) {
        console.error("Update team error:", error);
        res.status(500).json({ message: "Error updating team" });
    }
};

// Delete team
export const deleteTeam = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;

        const team = await Team.findById(parseInt(id));
        if (!team) {
            return res.status(404).json({ message: "Team not found" });
        }

        // Check if team has members
        const memberCount = await team.getMembersCount();
        if (memberCount > 0) {
            return res.status(400).json({
                message:
                    "Cannot delete team with members. Remove all members first.",
                memberCount,
            });
        }

        await team.destroy();

        res.status(200).json({ message: "Team deleted successfully" });
    } catch (error) {
        console.error("Delete team error:", error);
        res.status(500).json({ message: "Error deleting team" });
    }
};

// Add user to team
export const addUserToTeam = async (req: Request, res: Response) => {
    try {
        const { teamId, userId, role = "member" } = req.body;

        if (!teamId || !userId) {
            return res.status(400).json({
                message: "Team ID and User ID are required",
            });
        }

        const [team, user] = await Promise.all([
            Team.findById(teamId),
            User.findById(userId)
        ]);

        if (!team) {
            return res.status(404).json({ message: "Team not found" });
        }

        if (!user) {
            return res.status(404).json({ message: "User not found" });
        }

        // Check if user is already in team
        const isAlreadyMember = await user.isInTeam(teamId);
        if (isAlreadyMember) {
            return res.status(400).json({
                message: "User is already a member of this team",
            });
        }

        await user.joinTeam(teamId, role, req.user?.id);

        res.status(200).json({
            message: "User added to team successfully",
            teamUser: {
                userId,
                teamId,
                role,
                teamName: team.name,
                userName: `${user.firstName} ${user.lastName}`,
            },
        });
    } catch (error) {
        console.error("Add user to team error:", error);
        res.status(500).json({ message: "Error adding user to team" });
    }
};

// Remove user from team
export const removeUserFromTeam = async (req: Request, res: Response) => {
    try {
        const { teamId, userId } = req.body;

        if (!teamId || !userId) {
            return res.status(400).json({
                message: "Team ID and User ID are required",
            });
        }

        // Check if user exists
        const user = await User.findById(userId);
        if (!user) {
            return res.status(404).json({ message: "User not found" });
        }

        // Check if user is in team
        const isInTeam = await user.isInTeam(teamId);
        if (!isInTeam) {
            return res.status(400).json({
                message: "User is not a member of this team",
            });
        }

        await user.leaveTeam(teamId);

        res.status(200).json({
            message: "User removed from team successfully",
        });
    } catch (error) {
        console.error("Remove user from team error:", error);
        res.status(500).json({ message: "Error removing user from team" });
    }
};

// Get team members
export const getTeamMembers = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;

        const team = await Team.findById(parseInt(id));
        if (!team) {
            return res.status(404).json({ message: "Team not found" });
        }

        const members = await team.getMembers();

        res.status(200).json({
            message: "Team members retrieved successfully",
            team: {
                id: team.id,
                name: team.name,
            },
            members,
        });
    } catch (error) {
        console.error("Get team members error:", error);
        res.status(500).json({ message: "Error retrieving team members" });
    }
};
