import { DataTypes, Optional } from "sequelize";
import sequelize from "../config/database";
import BaseModel from "./BaseModel";
import TeamUser from "./TeamUser";
import User from "./User";

interface TeamAttributes {
    id: number;
    name: string;
    description?: string;
    leaderId?: number; // User ID of the team leader
    isActive: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface TeamCreationAttributes
    extends Optional<
        TeamAttributes,
        | "id"
        | "description"
        | "leaderId"
        | "isActive"
        | "createdAt"
        | "updatedAt"
    > {}

class Team
    extends BaseModel<TeamAttributes, TeamCreationAttributes>
    implements TeamAttributes
{
    public id!: number;
    public name!: string;
    public description?: string;
    public leaderId?: number;
    public isActive!: boolean;
    public readonly createdAt!: Date;
    public readonly updatedAt!: Date;

    // Static method to get team by name
    static async findByName(name: string): Promise<Team | null> {
        return this.findOne({ where: { name } });
    }

    // Static method to get all active teams
    static async getActiveTeams(): Promise<Team[]> {
        return this.findAll({
            where: { isActive: true },
            order: [["name", "ASC"]],
        });
    }

    // Static method to get teams by leader
    static async getTeamsByLeader(leaderId: number): Promise<Team[]> {
        return this.findAll({
            where: { leaderId, isActive: true },
            order: [["name", "ASC"]],
        });
    }

    // Method to get team members count
    public async getMembersCount(): Promise<number> {
        return TeamUser.countRecords({
            where: { teamId: this.id },
        });
    }

    // Method to get team members
    public async getMembers(): Promise<any[]> {
        const teamUsers = await TeamUser.findAll({
            where: { teamId: this.id },
            include: [
                {
                    model: User,
                    attributes: [
                        "id",
                        "firstName",
                        "lastName",
                        "email",
                        "phone",
                    ],
                    as: 'User'
                },
            ],
            order: [["joinedAt", "ASC"]],
        });

        return teamUsers.map((tu: any) => ({
            ...tu.User.toJSON(),
            joinedAt: tu.joinedAt,
            role: tu.role,
        }));
    }

    // Method to check if user is team leader
    public isLeader(userId: number): boolean {
        return this.leaderId === userId;
    }

    // Method to check if user is team member
    public async hasMember(userId: number): Promise<boolean> {
        const teamUser = await TeamUser.findOne({
            where: { teamId: this.id, userId },
        });
        return !!teamUser;
    }

    // Method to activate/deactivate team
    public async setActive(isActive: boolean): Promise<void> {
        await this.update({ isActive });
    }
}

Team.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false,
            unique: true,
            validate: {
                notEmpty: true,
                len: [2, 100],
            },
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        leaderId: {
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: "users",
                key: "id",
            },
            onDelete: "SET NULL",
            onUpdate: "CASCADE",
        },
        isActive: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true,
        },
    },
    {
        sequelize,
        modelName: "Team",
        tableName: "teams",
        indexes: [
            {
                unique: true,
                fields: ["name"],
            },
            {
                fields: ["leaderId"],
            },
            {
                fields: ["isActive"],
            },
        ],
    }
);

export default Team;
