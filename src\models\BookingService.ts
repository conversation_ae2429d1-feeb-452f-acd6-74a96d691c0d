import { DataTypes, Optional } from "sequelize";
import sequelize from "../config/database";
import BaseModel from "./BaseModel";

interface BookingServiceAttributes {
    id: number;
    bookingId: number;
    serviceId: number;
    isMainService: boolean; // true for the main service, false for additional services
    createdAt?: Date;
    updatedAt?: Date;
}

interface BookingServiceCreationAttributes
    extends Optional<BookingServiceAttributes, "id" | "createdAt" | "updatedAt"> {}

class BookingService
    extends BaseModel<BookingServiceAttributes, BookingServiceCreationAttributes>
    implements BookingServiceAttributes
{
    public id!: number;
    public bookingId!: number;
    public serviceId!: number;
    public isMainService!: boolean;
    public readonly createdAt!: Date;
    public readonly updatedAt!: Date;

    // Association properties
    public Booking?: any;
    public Service?: any;
}

BookingService.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        bookingId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: "bookings",
                key: "id",
            },
            onDelete: "CASCADE",
            onUpdate: "CASCADE",
        },
        serviceId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: "services",
                key: "id",
            },
            onDelete: "CASCADE",
            onUpdate: "CASCADE",
        },
        isMainService: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
            comment: "True for the main service, false for additional services",
        },
    },
    {
        sequelize,
        modelName: "BookingService",
        tableName: "booking_services",
        indexes: [
            {
                unique: true,
                fields: ["bookingId", "serviceId"],
                name: "unique_booking_service",
            },
            {
                fields: ["bookingId"],
                name: "booking_service_booking_id_index",
            },
            {
                fields: ["serviceId"],
                name: "booking_service_service_id_index",
            },
        ],
    }
);

export default BookingService;
