import { DataTypes, Optional } from "sequelize";
import sequelize from "../config/database";
import BaseModel from "./BaseModel";

interface ServiceAttributes {
    id: number;
    name: string;
    description: string;
    price: number;
    duration: number; // in minutes
    createdAt?: Date;
    updatedAt?: Date;
}

interface ServiceCreationAttributes
    extends Optional<ServiceAttributes, "id" | "createdAt" | "updatedAt"> {}

class Service
    extends BaseModel<ServiceAttributes, ServiceCreationAttributes>
    implements ServiceAttributes
{
    public id!: number;
    public name!: string;
    public description!: string;
    public price!: number;
    public duration!: number;
    public readonly createdAt!: Date;
    public readonly updatedAt!: Date;
}

Service.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
        price: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: false,
        },
        duration: {
            type: DataTypes.INTEGER,
            allowNull: false,
            comment: "Duration in minutes",
        },
    },
    {
        sequelize,
        modelName: "Service",
        tableName: "services",
    }
);

export default Service;
