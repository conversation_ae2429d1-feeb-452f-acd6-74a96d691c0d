import express from "express";
import * as serviceController from "../controllers/serviceController";
import { authenticate, authorize } from "../middleware/auth";

const router = express.Router();

router.get("/", serviceController.getAllServices);
router.get("/:id", serviceController.getServiceById);
router.post(
    "/",
    authenticate,
    authorize(["admin"]),
    serviceController.createService
);
router.put(
    "/:id",
    authenticate,
    authorize(["admin"]),
    serviceController.updateService
);
router.delete(
    "/:id",
    authenticate,
    authorize(["admin"]),
    serviceController.deleteService
);

export default router;
